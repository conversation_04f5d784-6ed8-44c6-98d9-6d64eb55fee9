-- RemoteSetup.lua (<PERSON>rip<PERSON>)
-- Automatically sets up required RemoteEvents and RemoteFunction for the NPC system

print("=== Setting up NPC RemoteEvents ===")

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create NPCRemotes folder
local npcRemotesFolder = ReplicatedStorage:FindFirstChild("NPCRemotes")
if not npcRemotesFolder then
    npcRemotesFolder = Instance.new("Folder")
    npcRemotesFolder.Name = "NPCRemotes"
    npcRemotesFolder.Parent = ReplicatedStorage
end

-- Create required RemoteEvents
local remoteEvents = {"PurchaseNPC", "SendCommand", "NPCStatusUpdate"}
for _, eventName in ipairs(remoteEvents) do
    if not npcRemotesFolder:FindFirstChild(eventName) then
        local remoteEvent = Instance.new("RemoteEvent")
        remoteEvent.Name = eventName
        remoteEvent.Parent = npcRemotesFolder
    end
end

-- Create required RemoteFunction
if not npcRemotesFolder:FindFirstChild("GetNPCList") then
    local remoteFunction = Instance.new("RemoteFunction")
    remoteFunction.Name = "GetNPCList"
    remoteFunction.Parent = npcRemotesFolder
end

print("✓ NPC System RemoteEvents setup complete!")
print("=== RemoteEvents are ready for use ===")
