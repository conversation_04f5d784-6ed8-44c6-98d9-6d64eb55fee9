-- Movement.lua (ModuleScript)
-- Handles NPC movement and pathfinding

local Movement = {}
Movement.__index = Movement

local PathfindingService = game:GetService("PathfindingService")
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local NPCUtils = require(ReplicatedStorage.NPCSystem.Utils.NPCUtils)

function Movement.new(npc)
    local self = setmetatable({}, Movement)

    self.npc = npc
    self.humanoid = npc.model:FindFirstChild("Humanoid")
    self.rootPart = NPCUtils.getRootPart(npc.model)
    
    self.currentTarget = nil
    self.isMoving = false
    self.path = nil
    self.waypoints = {}
    self.currentWaypointIndex = 1
    
    -- Movement modes
    self.mode = "idle" -- idle, follow, goto, patrol, guard
    self.guardPosition = nil
    self.patrolWaypoints = {}
    self.patrolIndex = 1
    
    -- Movement settings
    self.walkSpeed = 16
    self.runSpeed = 24
    self.followDistance = 5
    self.guardRadius = 10
    
    if self.humanoid then
        self.humanoid.WalkSpeed = self.walkSpeed
    end
    
    return self
end

function Movement:update(deltaTime)
    if not self.humanoid or not self.rootPart then return end
    
    if self.mode == "follow" and self.currentTarget then
        self:updateFollow()
    elseif self.mode == "patrol" then
        self:updatePatrol()
    elseif self.mode == "guard" then
        self:updateGuard()
    end
end

function Movement:followTarget(target)
    if not target then return false end
    
    self.currentTarget = target
    self.mode = "follow"
    self.isMoving = true
    
    print(self.npc.name, "following", target.Name or "target")
    return true
end

function Movement:updateFollow()
    if not self.currentTarget or not self.currentTarget.Parent then
        self:stop()
        return
    end
    
    local targetPart = NPCUtils.getRootPart(self.currentTarget)
    if not targetPart then
        self:stop()
        return
    end
    
    local distance = (self.rootPart.Position - targetPart.Position).Magnitude
    
    if distance > self.followDistance then
        self:moveToPosition(targetPart.Position)
    else
        self.humanoid:MoveTo(self.rootPart.Position) -- Stop moving
    end
end

function Movement:moveTo(position)
    if not position then return false end
    
    self.mode = "goto"
    self.isMoving = true
    
    return self:moveToPosition(position)
end

function Movement:moveToPosition(position)
    if not self.humanoid or not self.rootPart then return false end
    
    -- Try simple movement first
    local distance = (self.rootPart.Position - position).Magnitude
    if distance < 50 then
        self.humanoid:MoveTo(position)
        return true
    end
    
    -- Use pathfinding for longer distances
    return self:pathfindTo(position)
end

function Movement:pathfindTo(targetPosition)
    if not self.rootPart then return false end
    
    local path = PathfindingService:CreatePath({
        AgentRadius = 2,
        AgentHeight = 5,
        AgentCanJump = true,
        WaypointSpacing = 4
    })
    
    local success, errorMessage = pcall(function()
        path:ComputeAsync(self.rootPart.Position, targetPosition)
    end)
    
    if not success then
        warn("Pathfinding failed:", errorMessage)
        -- Fallback to direct movement
        self.humanoid:MoveTo(targetPosition)
        return false
    end
    
    if path.Status == Enum.PathStatus.Success then
        self.path = path
        self.waypoints = path:GetWaypoints()
        self.currentWaypointIndex = 1
        self:moveToNextWaypoint()
        return true
    else
        warn("Path computation failed:", path.Status)
        self.humanoid:MoveTo(targetPosition)
        return false
    end
end

function Movement:moveToNextWaypoint()
    if not self.waypoints or self.currentWaypointIndex > #self.waypoints then
        self:stop()
        return
    end
    
    local waypoint = self.waypoints[self.currentWaypointIndex]
    self.humanoid:MoveTo(waypoint.Position)
    
    if waypoint.Action == Enum.PathWaypointAction.Jump then
        self.humanoid.Jump = true
    end
    
    -- Connect to MoveToFinished to move to next waypoint
    local connection
    connection = self.humanoid.MoveToFinished:Connect(function(reached)
        connection:Disconnect()
        if reached then
            self.currentWaypointIndex = self.currentWaypointIndex + 1
            self:moveToNextWaypoint()
        else
            -- Try again or give up
            self:stop()
        end
    end)
end

function Movement:stop()
    self.mode = "idle"
    self.isMoving = false
    self.currentTarget = nil
    self.path = nil
    self.waypoints = {}
    
    if self.humanoid then
        self.humanoid:MoveTo(self.rootPart.Position)
    end
    
    print(self.npc.name, "stopped moving")
    return true
end

function Movement:setGuardPosition(position)
    self.guardPosition = position or self.rootPart.Position
    self.mode = "guard"
    self.isMoving = false
    
    print(self.npc.name, "guarding position", tostring(self.guardPosition))
    return true
end

function Movement:updateGuard()
    if not self.guardPosition then return end
    
    local distance = (self.rootPart.Position - self.guardPosition).Magnitude
    if distance > self.guardRadius then
        self:moveToPosition(self.guardPosition)
    end
end

function Movement:startPatrol(waypoints)
    if not waypoints or #waypoints < 2 then return false end
    
    self.patrolWaypoints = waypoints
    self.patrolIndex = 1
    self.mode = "patrol"
    
    self:moveToPatrolWaypoint()
    return true
end

function Movement:moveToPatrolWaypoint()
    if not self.patrolWaypoints or #self.patrolWaypoints == 0 then
        self:stop()
        return
    end
    
    local waypoint = self.patrolWaypoints[self.patrolIndex]
    self:moveToPosition(waypoint)
    
    -- Move to next waypoint when reached
    local connection
    connection = self.humanoid.MoveToFinished:Connect(function(reached)
        connection:Disconnect()
        if reached then
            wait(2) -- Pause at waypoint
            self.patrolIndex = self.patrolIndex + 1
            if self.patrolIndex > #self.patrolWaypoints then
                self.patrolIndex = 1 -- Loop back to start
            end
            self:moveToPatrolWaypoint()
        end
    end)
end

function Movement:updatePatrol()
    -- Patrol logic is handled by moveToPatrolWaypoint and its connections
end

function Movement:setSpeed(speed)
    self.walkSpeed = speed
    if self.humanoid then
        self.humanoid.WalkSpeed = speed
    end
end

function Movement:destroy()
    self:stop()
    self.npc = nil
    self.humanoid = nil
    self.rootPart = nil
end

return Movement
