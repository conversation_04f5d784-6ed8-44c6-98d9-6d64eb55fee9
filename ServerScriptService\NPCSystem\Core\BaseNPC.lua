-- BaseNPC.lua (ModuleScript)
-- Core NPC class that handles basic NPC functionality

local BaseNPC = {}
BaseNPC.__index = BaseNPC

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import components and utilities
local Movement = require(script.Parent.Parent.Components.Movement)
local Health = require(script.Parent.Parent.Components.Health)
local NPCUtils = require(ReplicatedStorage.NPCSystem.Utils.NPCUtils)

function BaseNPC.new(npcModel, config)
    local self = setmetatable({}, BaseNPC)
    
    self.model = npcModel
    self.config = config or {}
    self.id = self.config.id or NPCUtils.generateId()
    self.name = self.config.name or "Unnamed NPC"
    self.isActive = true
    self.owner = nil
    
    -- Initialize components
    self.movement = Movement.new(self)
    self.health = Health.new(self)
    
    -- Set up NPC properties
    self:setupNPC()
    
    return self
end

function BaseNPC:setupNPC()
    if not self.model then return end
    
    -- Set up humanoid if it exists
    local humanoid = self.model:FindFirstChild("Humanoid")
    if humanoid then
        humanoid.Name = self.name
        humanoid.DisplayDistanceType = Enum.HumanoidDisplayDistanceType.None
    end
    
    -- Add identification
    local stringValue = Instance.new("StringValue")
    stringValue.Name = "NPCId"
    stringValue.Value = self.id
    stringValue.Parent = self.model
end

function BaseNPC:update(deltaTime)
    if not self.isActive then return end
    
    -- Update components
    if self.movement then
        self.movement:update(deltaTime)
    end
    
    if self.health then
        self.health:update(deltaTime)
    end
end

function BaseNPC:setOwner(player)
    self.owner = player
    print(self.name .. " is now owned by " .. player.Name)
end

function BaseNPC:executeCommand(command, ...)
    if not self.isActive then return false end
    
    local args = {...}
    print("Executing command:", command, "on", self.name)
    
    -- Handle basic commands
    if command == "follow" and self.movement then
        return self.movement:followTarget(args[1])
    elseif command == "stop" and self.movement then
        return self.movement:stop()
    elseif command == "goto" and self.movement then
        return self.movement:moveTo(args[1])
    end
    
    return false
end

function BaseNPC:destroy()
    self.isActive = false
    
    if self.movement then
        self.movement:destroy()
    end
    
    if self.health then
        self.health:destroy()
    end
    
    if self.model then
        self.model:Destroy()
    end
end

return BaseNPC
