-- PurchaseInterface.lua (ModuleScript)
-- Interface for purchasing NPCs

local PurchaseInterface = {}
PurchaseInterface.__index = PurchaseInterface

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

-- Create a new instance of PurchaseInterface
function PurchaseInterface.new(player)
    local self = setmetatable({}, PurchaseInterface)

    self.player = player
    self.gui = nil
    self.purchaseFrame = nil
    self.overlay = nil

    -- Remote events
    self.NPCRemotes = ReplicatedStorage:WaitForChild("NPCRemotes", 5)
    if not self.NPCRemotes then
        return nil
    end

    self.PurchaseNPC = self.NPCRemotes:WaitForChild("PurchaseNPC", 5)
    self.NPCStatusUpdate = self.NPCRemotes:WaitForChild("NPCStatusUpdate", 5)

    if not self.PurchaseNPC or not self.NPCStatusUpdate then
        return nil
    end

    -- Import NPC configs
    local npcSystem = ReplicatedStorage:WaitForChild("NPCSystem", 5)
    if not npcSystem then
        return nil
    end

    local dataFolder = npcSystem:WaitForChild("Data", 5)
    if not dataFolder then
        return nil
    end

    self.NPCConfigs = dataFolder:WaitForChild("NPCConfigs", 5)
    if not self.NPCConfigs then
        return nil
    end
    return self
end

function PurchaseInterface:init(gui)
    print("PurchaseInterface:init() called with gui:", gui)
    self.gui = gui

    local success, error = pcall(function()
        self:createPurchaseFrame()
    end)

    if not success then
        warn("PurchaseInterface: Error creating purchase frame:", error)
        return
    end

    self:connectEvents()
    print("PurchaseInterface initialized successfully")
    print("purchaseFrame:", self.purchaseFrame)
    print("overlay:", self.overlay)
end

function PurchaseInterface:createPurchaseFrame()
    print("PurchaseInterface:createPurchaseFrame() called")
    print("self.gui:", self.gui)

    if not self.gui then
        warn("PurchaseInterface: self.gui is nil, cannot create purchase frame!")
        return
    end

    print("Creating purchase frame elements...")

    -- Background overlay (create first, lower ZIndex)
    local overlay = Instance.new("Frame")
    overlay.Name = "Overlay"
    overlay.Size = UDim2.new(1, 0, 1, 0)
    overlay.Position = UDim2.new(0, 0, 0, 0)
    overlay.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    overlay.BackgroundTransparency = 0.5
    overlay.BorderSizePixel = 0
    overlay.Visible = false
    overlay.ZIndex = 1
    overlay.Parent = self.gui

    -- Purchase frame (create after overlay, higher ZIndex)
    local purchaseFrame = Instance.new("Frame")
    purchaseFrame.Name = "PurchaseFrame"
    purchaseFrame.Size = UDim2.new(0, 500, 0, 400)
    purchaseFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
    purchaseFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    purchaseFrame.BorderSizePixel = 0
    purchaseFrame.Visible = false
    purchaseFrame.ZIndex = 2
    purchaseFrame.Parent = self.gui

    local frameCorner = Instance.new("UICorner")
    frameCorner.CornerRadius = UDim.new(0, 8)
    frameCorner.Parent = purchaseFrame
    
    -- Title bar
    local titleBar = Instance.new("Frame")
    titleBar.Name = "TitleBar"
    titleBar.Size = UDim2.new(1, 0, 0, 40)
    titleBar.Position = UDim2.new(0, 0, 0, 0)
    titleBar.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    titleBar.BorderSizePixel = 0
    titleBar.ZIndex = 3
    titleBar.Parent = purchaseFrame

    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 8)
    titleCorner.Parent = titleBar

    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Size = UDim2.new(1, -50, 1, 0)
    title.Position = UDim2.new(0, 10, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "Purchase NPC"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.ZIndex = 4
    title.Parent = titleBar

    -- Close button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.ZIndex = 4
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 4)
    closeCorner.Parent = closeButton
    
    -- NPC types scroll frame
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Name = "NPCTypesFrame"
    scrollFrame.Size = UDim2.new(1, -20, 1, -60)
    scrollFrame.Position = UDim2.new(0, 10, 0, 50)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.BorderSizePixel = 0
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, 0)
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.ZIndex = 3
    scrollFrame.Parent = purchaseFrame
    
    -- Store references
    self.purchaseFrame = purchaseFrame
    self.overlay = overlay

    print("PurchaseInterface: References stored successfully")
    print("self.purchaseFrame:", self.purchaseFrame)
    print("self.overlay:", self.overlay)

    -- Connect events
    closeButton.MouseButton1Click:Connect(function()
        self:hideInterface()
    end)

    overlay.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            self:hideInterface()
        end
    end)

    print("PurchaseInterface: Events connected")

    print("PurchaseInterface: createPurchaseFrame() completed successfully")
end

function PurchaseInterface:loadNPCTypes()
    local scrollFrame = self.purchaseFrame:FindFirstChild("NPCTypesFrame")
    if not scrollFrame then
        warn("PurchaseInterface: NPCTypesFrame not found!")
        return
    end

    print("PurchaseInterface: Loading NPC types...")

    -- Clear existing items first
    for _, child in pairs(scrollFrame:GetChildren()) do
        if child:IsA("Frame") and child.Name:match("NPCType_") then
            child:Destroy()
        end
    end

    -- Get NPC configs
    local success, configs = pcall(function()
        return require(self.NPCConfigs)
    end)

    if not success then
        warn("PurchaseInterface: Failed to load NPCConfigs:", configs)
        return
    end

    print("PurchaseInterface: Raw configs loaded:", configs)

    local yOffset = 0
    local npcCount = 0

    for npcType, config in pairs(configs) do
        -- Only process actual NPC configurations (tables with name field)
        if type(config) == "table" and config.name then
            print("PurchaseInterface: Creating item for NPC:", npcType, "with name:", config.name)
            self:createNPCTypeItem(npcType, config, yOffset, scrollFrame)
            yOffset = yOffset + 120
            npcCount = npcCount + 1
        else
            print("PurchaseInterface: Skipping non-NPC entry:", npcType, "type:", type(config))
        end
    end

    print("PurchaseInterface: Loaded", npcCount, "NPC types")

    -- Update canvas size
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yOffset + 10)
end

function PurchaseInterface:createNPCTypeItem(npcType, config, yOffset, parent)
    local item = Instance.new("Frame")
    item.Name = "NPCType_" .. npcType
    item.Size = UDim2.new(1, -10, 0, 110)
    item.Position = UDim2.new(0, 5, 0, yOffset)
    item.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    item.BorderSizePixel = 0
    item.ZIndex = 4
    item.Parent = parent
    
    local itemCorner = Instance.new("UICorner")
    itemCorner.CornerRadius = UDim.new(0, 6)
    itemCorner.Parent = item
    
    -- NPC name
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Name = "NameLabel"
    nameLabel.Size = UDim2.new(1, -120, 0, 25)
    nameLabel.Position = UDim2.new(0, 10, 0, 5)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = config.name or npcType
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.ZIndex = 5
    nameLabel.Parent = item

    -- Cost
    local costLabel = Instance.new("TextLabel")
    costLabel.Name = "CostLabel"
    costLabel.Size = UDim2.new(0, 100, 0, 25)
    costLabel.Position = UDim2.new(1, -110, 0, 5)
    costLabel.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    costLabel.BorderSizePixel = 0
    local cost = config.cost or 100
    costLabel.Text = cost == 0 and "🆓 Free" or ("💰 " .. cost)
    costLabel.TextColor3 = Color3.fromRGB(255, 215, 0)
    costLabel.TextScaled = true
    costLabel.Font = Enum.Font.SourceSansBold
    costLabel.ZIndex = 5
    costLabel.Parent = item
    
    local costCorner = Instance.new("UICorner")
    costCorner.CornerRadius = UDim.new(0, 4)
    costCorner.Parent = costLabel
    
    -- Description
    local descLabel = Instance.new("TextLabel")
    descLabel.Name = "DescLabel"
    descLabel.Size = UDim2.new(1, -20, 0, 40)
    descLabel.Position = UDim2.new(0, 10, 0, 35)
    descLabel.BackgroundTransparency = 1
    descLabel.Text = config.description or "A loyal NPC companion."
    descLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    descLabel.TextScaled = true
    descLabel.Font = Enum.Font.SourceSans
    descLabel.TextXAlignment = Enum.TextXAlignment.Left
    descLabel.TextWrapped = true
    descLabel.Parent = item
    
    -- Stats
    local statsLabel = Instance.new("TextLabel")
    statsLabel.Name = "StatsLabel"
    statsLabel.Size = UDim2.new(1, -120, 0, 20)
    statsLabel.Position = UDim2.new(0, 10, 0, 80)
    statsLabel.BackgroundTransparency = 1
    statsLabel.Text = string.format("HP: %d | Speed: %d", 
        config.maxHealth or 100, 
        config.walkSpeed or 16)
    statsLabel.TextColor3 = Color3.fromRGB(150, 150, 150)
    statsLabel.TextScaled = true
    statsLabel.Font = Enum.Font.SourceSans
    statsLabel.TextXAlignment = Enum.TextXAlignment.Left
    statsLabel.Parent = item
    
    -- Purchase button
    local purchaseButton = Instance.new("TextButton")
    purchaseButton.Name = "PurchaseButton"
    purchaseButton.Size = UDim2.new(0, 100, 0, 25)
    purchaseButton.Position = UDim2.new(1, -110, 0, 80)
    purchaseButton.BackgroundColor3 = Color3.fromRGB(50, 150, 50)
    purchaseButton.BorderSizePixel = 0
    purchaseButton.Text = cost == 0 and "Get Free" or "Purchase"
    purchaseButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    purchaseButton.TextScaled = true
    purchaseButton.Font = Enum.Font.SourceSansBold
    purchaseButton.ZIndex = 10  -- Make sure button is on top
    purchaseButton.Parent = item
    
    local purchaseCorner = Instance.new("UICorner")
    purchaseCorner.CornerRadius = UDim.new(0, 4)
    purchaseCorner.Parent = purchaseButton
    
    -- Button events
    print("Connecting button click event for NPC:", npcType)
    print("Button created with size:", purchaseButton.Size)
    print("Button position:", purchaseButton.Position)
    print("Button parent:", purchaseButton.Parent)

    -- Add a simple hover test to verify the button is interactive
    purchaseButton.MouseEnter:Connect(function()
        print("🖱️ Mouse entered button for:", npcType)
        purchaseButton.BackgroundColor3 = Color3.fromRGB(70, 170, 70)
    end)

    purchaseButton.MouseLeave:Connect(function()
        print("🖱️ Mouse left button for:", npcType)
        purchaseButton.BackgroundColor3 = Color3.fromRGB(50, 150, 50)
    end)

    purchaseButton.MouseButton1Click:Connect(function()
        print("🔥 BUTTON CLICKED! NPC:", npcType)
        print("Config exists:", config ~= nil)
        print("Self exists:", self ~= nil)
        print("PurchaseNPC remote exists:", self.PurchaseNPC ~= nil)

        -- Try to call purchaseNPC with error handling
        local success, error = pcall(function()
            self:purchaseNPC(npcType, config)
        end)

        if not success then
            warn("Error in purchaseNPC:", error)
        end
    end)

    print("Button click event connected for:", npcType)
    
    -- Hover effects
    self:addHoverEffect(purchaseButton, Color3.fromRGB(70, 170, 70))
end

function PurchaseInterface:addHoverEffect(button, hoverColor)
    local originalColor = button.BackgroundColor3
    
    button.MouseEnter:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {BackgroundColor3 = hoverColor})
        tween:Play()
    end)
    
    button.MouseLeave:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {BackgroundColor3 = originalColor})
        tween:Play()
    end)
end

function PurchaseInterface:connectEvents()
    -- Listen for purchase results
    self.NPCStatusUpdate.OnClientEvent:Connect(function(status, data)
        if status == "purchased" then
            self:showMessage("Successfully purchased " .. data.name .. "!")
            -- Wait 2 seconds before hiding so user can see the success message
            spawn(function()
                wait(2)
                self:hideInterface()
            end)
        elseif status == "purchase_failed" then
            self:showMessage("Purchase failed: " .. (data.reason or "Unknown error"))
        end
    end)
end

function PurchaseInterface:purchaseNPC(npcType, config)
    print("=== PurchaseInterface:purchaseNPC called ===")
    print("NPC Type:", npcType)
    print("Config:", config)
    print("PurchaseNPC remote:", self.PurchaseNPC)

    -- Show confirmation (optional)
    local cost = config.cost or 100
    local confirmMessage
    if cost == 0 then
        confirmMessage = string.format("Get %s for free?", config.name or npcType)
    else
        confirmMessage = string.format("Purchase %s for %d coins?", config.name or npcType, cost)
    end

    print("Firing server with npcType:", npcType)

    -- For now, directly purchase. You could add a confirmation dialog here
    if self.PurchaseNPC then
        self.PurchaseNPC:FireServer(npcType)
        print("Server request sent successfully")
    else
        warn("PurchaseNPC remote is nil!")
        return
    end

    local requestMessage = cost == 0 and "Getting free NPC..." or "Purchase request sent..."
    self:showMessage(requestMessage)
    print("=== PurchaseInterface:purchaseNPC completed ===")
end

function PurchaseInterface:showInterface()
    print("PurchaseInterface:showInterface() called")

    if not self.purchaseFrame then
        warn("PurchaseInterface: purchaseFrame is nil!")
        return
    end

    if not self.overlay then
        warn("PurchaseInterface: overlay is nil!")
        return
    end

    print("PurchaseInterface: Setting overlay and frame visible")
    self.overlay.Visible = true
    self.purchaseFrame.Visible = true

    -- Load NPC types when showing the interface
    print("PurchaseInterface: Loading NPC types...")
    self:loadNPCTypes()

    -- Animate in
    self.purchaseFrame.Size = UDim2.new(0, 0, 0, 0)
    self.purchaseFrame.Position = UDim2.new(0.5, 0, 0.5, 0)

    print("PurchaseInterface: Starting animation")
    local tween = TweenService:Create(self.purchaseFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
        Size = UDim2.new(0, 500, 0, 400),
        Position = UDim2.new(0.5, -250, 0.5, -200)
    })
    tween:Play()

    print("PurchaseInterface: Interface should now be visible")
end

function PurchaseInterface:hideInterface()
    if not self.purchaseFrame or not self.overlay then return end
    
    -- Animate out
    local tween = TweenService:Create(self.purchaseFrame, TweenInfo.new(0.2), {
        Size = UDim2.new(0, 0, 0, 0),
        Position = UDim2.new(0.5, 0, 0.5, 0)
    })
    
    tween.Completed:Connect(function()
        self.purchaseFrame.Visible = false
        self.overlay.Visible = false
    end)
    
    tween:Play()
end

function PurchaseInterface:showMessage(message)
    print("PurchaseInterface:", message)

    -- Create a temporary message label
    if self.purchaseFrame then
        local messageLabel = Instance.new("TextLabel")
        messageLabel.Name = "MessageLabel"
        messageLabel.Size = UDim2.new(1, -20, 0, 40)
        messageLabel.Position = UDim2.new(0, 10, 1, -50)
        messageLabel.BackgroundColor3 = Color3.fromRGB(0, 150, 0)
        messageLabel.BorderSizePixel = 0
        messageLabel.Text = message
        messageLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        messageLabel.TextScaled = true
        messageLabel.Font = Enum.Font.SourceSansBold
        messageLabel.Parent = self.purchaseFrame

        local corner = Instance.new("UICorner")
        corner.CornerRadius = UDim.new(0, 4)
        corner.Parent = messageLabel

        -- Remove message after 3 seconds
        game:GetService("Debris"):AddItem(messageLabel, 3)
    end
end

-- Debug function to test the interface
function PurchaseInterface:testInterface()
    print("Testing PurchaseInterface...")
    if self.gui then
        print("GUI exists:", self.gui.Name)
        if self.purchaseFrame then
            print("Purchase frame exists, making it visible for test...")
            self.overlay.Visible = true
            self.purchaseFrame.Visible = true
            self.purchaseFrame.Size = UDim2.new(0, 500, 0, 400)
            self.purchaseFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
            print("Test interface should now be visible")
        else
            warn("Purchase frame is nil!")
        end
    else
        warn("GUI is nil!")
    end
end

return PurchaseInterface
