# NPC System for Roblox

A comprehensive NPC (Non-Player Character) system for Roblox games that allows players to purchase, command, and manage AI-controlled companions.

## Features

- **NPC Purchase System**: Players can buy different types of NPCs with in-game currency
- **Command Interface**: Intuitive GUI for sending commands to NPCs
- **Multiple NPC Types**: Gunslinger, Warrior, and other customizable classes
- **Advanced AI**: NPCs can follow, patrol, guard, attack, and perform special abilities
- **Session-Based Data**: Player NPC ownership and currency persist during game session
- **Modular Design**: Easy to extend with new NPC types and commands

## File Structure

```
ServerScriptService/
├── NPCSystem/
│   ├── Core/
│   │   ├── BaseNPC.lua (ModuleScript)
│   │   ├── NPCManager.lua (Script)
│   │   └── CommandHandler.lua (ModuleScript)
│   ├── Components/
│   │   ├── Movement.lua (ModuleScript)
│   │   └── Health.lua (ModuleScript)
│   ├── Setup/
│   │   └── RemoteSetup.lua (Script)
│   └── Init.lua (Script)
└── SimpleNPCTest.lua (Script - Disabled)

StarterPlayerScripts/
└── NPCInterface/
    └── CommandInterface.lua (LocalScript)

StarterGui/
└── NPCInterface/
    ├── MainInterface.lua (LocalScript)
    └── PurchaseInterface.lua (LocalScript)

ReplicatedStorage/
├── NPCRemotes/
│   ├── PurchaseNPC (RemoteEvent)
│   ├── SendCommand (RemoteEvent)
│   ├── NPCStatusUpdate (RemoteEvent)
│   └── GetNPCList (RemoteFunction)
└── NPCSystem/
    ├── Data/
    │   ├── NPCConfigs.lua (ModuleScript)
    │   └── Commands.lua (ModuleScript)
    └── Utils/
        └── NPCUtils.lua (ModuleScript)
```

## Setup Instructions

### 1. File Placement
Copy all files to their respective locations in Roblox Studio:

- Place all `ServerScriptService` files in ServerScriptService
- Place all `StarterPlayerScripts` files in StarterPlayerScripts  
- Place all `StarterGui` files in StarterGui
- Place all `ReplicatedStorage` files in ReplicatedStorage

### 2. Quick Setup (Recommended)
Run the QuickSetup script to automatically configure everything:

1. In Roblox Studio, run `ServerScriptService/NPCSystem/Setup/QuickSetup.lua`
2. This will automatically:
   - Create all required RemoteEvents and RemoteFunction
   - Check file structure
   - Test DataStore compatibility
   - Verify character model compatibility

### 2. Manual Setup (Alternative)
If you prefer manual setup:

1. Create a Folder named "NPCRemotes" in ReplicatedStorage
2. Inside NPCRemotes, create:
   - RemoteEvent named "PurchaseNPC"
   - RemoteEvent named "SendCommand"
   - RemoteEvent named "NPCStatusUpdate"
   - RemoteFunction named "GetNPCList"
3. Ensure Components are ModuleScripts (not Scripts)
4. Enable Studio API access in Game Settings > Security

### 3. Enable DataStore API
1. Go to Game Settings in Roblox Studio
2. Navigate to Security tab
3. Enable "Allow HTTP Requests" 
4. Enable "Enable Studio Access to API Services"

### 4. Configure NPC Models (Optional)
The system includes basic NPC model creation. To use custom models:

1. Create your NPC models in ReplicatedStorage
2. Modify the `createNPCModel` function in `NPCManager.lua`
3. Update the appearance settings in `NPCConfigs.lua`

## Usage

### For Players

1. **Open NPC Interface**: The main interface should appear automatically
2. **Purchase NPCs**: Click "Purchase NPC" to see available types
3. **Select NPCs**: Click on an NPC in your list to select it
4. **Send Commands**: Use the command interface to control your NPCs

### Available Commands

- `follow <player>` - Make NPC follow a player
- `goto <x,y,z>` - Move NPC to coordinates  
- `stop` - Stop all NPC actions
- `guard` - Guard current position
- `attack <target>` - Attack a target
- `patrol <waypoints>` - Patrol between points
- `heal [target]` - Heal self or target (healers only)

### For Developers

#### Adding New NPC Types

1. Edit `ReplicatedStorage/NPCSystem/Data/NPCConfigs.lua`
2. Add a new configuration following the existing pattern:

```lua
newNPCType = {
    name = "New NPC",
    type = "newNPCType", 
    cost = 500,
    description = "Description here",
    maxHealth = 100,
    walkSpeed = 16,
    -- Add other properties...
}
```

#### Adding New Commands

1. Edit `ReplicatedStorage/NPCSystem/Data/Commands.lua`
2. Add command definition:

```lua
newCommand = {
    name = "New Command",
    description = "What it does",
    usage = "newcommand <args>",
    args = { required = 1 },
    category = "utility"
}
```

3. Implement command logic in `CommandHandler.lua`

#### Customizing Currency System

Edit the `DataService.lua` file to integrate with your game's currency system:

```lua
-- Modify these functions:
function DataService:updatePlayerCurrency(player, amount)
function DataService:canAfford(player, cost)
function DataService:getPlayerCurrency(player)
```

## API Reference

### BaseNPC Class

- `BaseNPC.new(model, config)` - Create new NPC
- `npc:setOwner(player)` - Set NPC owner
- `npc:executeCommand(command, ...)` - Execute command
- `npc:update(deltaTime)` - Update NPC (called automatically)

### NPCManager

- `NPCManager:createNPC(type, config, owner)` - Create NPC instance
- `NPCManager:handleCommand(player, npcId, command, ...)` - Process command
- `NPCManager:getPlayerNPCs(player)` - Get player's NPCs

### Movement Component

- `movement:followTarget(target)` - Follow a target
- `movement:moveTo(position)` - Move to position
- `movement:stop()` - Stop movement
- `movement:startPatrol(waypoints)` - Start patrol

## Troubleshooting

### Common Issues

1. **"Attempted to call require with invalid argument(s)"**
   - **Cause**: Component files are Scripts instead of ModuleScripts
   - **Fix**: Ensure Movement.lua and Health.lua are ModuleScripts, not Scripts
   - **Auto-fix**: Run QuickSetup.lua script

2. **"Infinite yield possible on NPCRemotes:WaitForChild"**
   - **Cause**: RemoteEvents don't exist or are .lua files instead of actual RemoteEvent instances
   - **Fix**: Create proper RemoteEvent instances in ReplicatedStorage/NPCRemotes
   - **Auto-fix**: Run QuickSetup.lua script

3. **"Torso is not a valid member of Model"**
   - **Cause**: R15 characters use HumanoidRootPart instead of Torso
   - **Fix**: Updated scripts now handle both R6 and R15 characters automatically
   - **Status**: ✅ Fixed in latest version

4. **"attempt to index nil with 'savePlayerData'"**
   - **Cause**: Incorrect self reference in DataService auto-save
   - **Fix**: Updated to use DataService: instead of self:
   - **Status**: ✅ Fixed in latest version

5. **DataStore errors in Studio**
   - **Cause**: Studio API access not enabled
   - **Fix**: Enable "Studio Access to API Services" in Game Settings > Security
   - **Note**: Scripts now handle Studio limitations gracefully

6. **NPCs not spawning**: Check that NPC models are being created properly in `createNPCModel`
7. **Commands not working**: Verify RemoteEvents are properly named and placed
8. **UI not showing**: Check that LocalScripts are in correct StarterGui/StarterPlayerScripts locations

### Debug Mode

Enable debug output by setting `game:GetService("RunService"):IsStudio()` checks in the code.

## Contributing

To contribute to this NPC system:

1. Follow the existing code structure and naming conventions
2. Add proper error handling and validation
3. Include documentation for new features
4. Test thoroughly in both Studio and live games

## License

This NPC system is provided as-is for educational and development purposes. Feel free to modify and use in your Roblox games.
