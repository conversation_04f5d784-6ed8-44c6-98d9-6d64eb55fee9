-- MainInterface.lua (LocalScript)
-- Main NPC interface for managing NPCs

local MainInterface = {}

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Remote events
local NPCRemotes = ReplicatedStorage:WaitForChild("NPCRemotes")
local GetNPCList = NPCRemotes:WaitForChild("GetNPCList")
local NPCStatusUpdate = NPCRemotes:WaitForChild("NPCStatusUpdate")

-- UI elements
MainInterface.gui = nil
MainInterface.mainFrame = nil
MainInterface.npcList = {}
MainInterface.selectedNPC = nil

-- Import other interfaces
local PurchaseInterfaceModule = require(script.Parent.PurchaseInterface)

-- We'll initialize CommandInterface after the GUI is created since it's in PlayerScripts
local CommandInterface = nil

-- Instance of PurchaseInterface
local PurchaseInterface = nil

function MainInterface:init()
    self:createGUI()
    self:connectEvents()

    -- Load NPC list in a separate thread
    spawn(function()
        self:loadNPCList()
    end)

    -- Initialize PurchaseInterface
    PurchaseInterface = PurchaseInterfaceModule.new(player)
    if PurchaseInterface then
        PurchaseInterface:init(self.gui)
    end

    -- Initialize CommandInterface from PlayerScripts
    spawn(function()
        local success, commandInterface = pcall(function()
            return require(player.PlayerScripts:WaitForChild("NPCInterface"):WaitForChild("CommandInterface"))
        end)

        if success then
            CommandInterface = commandInterface
            CommandInterface:init(self.gui)
        end
    end)
end

function MainInterface:createGUI()
    -- Create main ScreenGui
    local gui = Instance.new("ScreenGui")
    gui.Name = "NPCInterface"
    gui.ResetOnSpawn = false
    gui.Parent = playerGui
    
    -- Main frame
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 300, 0, 400)
    mainFrame.Position = UDim2.new(1, -310, 0, 10)
    mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    mainFrame.BorderSizePixel = 0
    mainFrame.Parent = gui
    
    -- Add corner radius
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = mainFrame
    
    -- Title bar
    local titleBar = Instance.new("Frame")
    titleBar.Name = "TitleBar"
    titleBar.Size = UDim2.new(1, 0, 0, 40)
    titleBar.Position = UDim2.new(0, 0, 0, 0)
    titleBar.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainFrame
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 8)
    titleCorner.Parent = titleBar
    
    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Size = UDim2.new(1, -80, 1, 0)
    title.Position = UDim2.new(0, 10, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "My NPCs"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = titleBar
    
    -- Close button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 4)
    closeCorner.Parent = closeButton
    
    -- NPC list frame
    local npcListFrame = Instance.new("ScrollingFrame")
    npcListFrame.Name = "NPCListFrame"
    npcListFrame.Size = UDim2.new(1, -20, 1, -100)
    npcListFrame.Position = UDim2.new(0, 10, 0, 50)
    npcListFrame.BackgroundTransparency = 1
    npcListFrame.BorderSizePixel = 0
    npcListFrame.CanvasSize = UDim2.new(0, 0, 0, 0)
    npcListFrame.ScrollBarThickness = 6
    npcListFrame.Parent = mainFrame
    
    -- Purchase button
    local purchaseButton = Instance.new("TextButton")
    purchaseButton.Name = "PurchaseButton"
    purchaseButton.Size = UDim2.new(1, -20, 0, 30)
    purchaseButton.Position = UDim2.new(0, 10, 1, -40)
    purchaseButton.BackgroundColor3 = Color3.fromRGB(50, 150, 50)
    purchaseButton.BorderSizePixel = 0
    purchaseButton.Text = "Get Free NPCs"
    purchaseButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    purchaseButton.TextScaled = true
    purchaseButton.Font = Enum.Font.SourceSansBold
    purchaseButton.Parent = mainFrame
    
    local purchaseCorner = Instance.new("UICorner")
    purchaseCorner.CornerRadius = UDim.new(0, 4)
    purchaseCorner.Parent = purchaseButton
    
    -- Create toggle button (always visible)
    local toggleButton = Instance.new("TextButton")
    toggleButton.Name = "ToggleButton"
    toggleButton.Size = UDim2.new(0, 100, 0, 30)
    toggleButton.Position = UDim2.new(1, -110, 0, 10)
    toggleButton.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    toggleButton.BorderSizePixel = 0
    toggleButton.Text = "NPC Menu"
    toggleButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    toggleButton.TextScaled = true
    toggleButton.Font = Enum.Font.SourceSansBold
    toggleButton.Parent = gui

    -- Add corner radius to toggle button
    local toggleCorner = Instance.new("UICorner")
    toggleCorner.CornerRadius = UDim.new(0, 4)
    toggleCorner.Parent = toggleButton

    -- Connect toggle button
    toggleButton.MouseButton1Click:Connect(function()
        self:toggleInterface()
    end)

    -- Add hover effect to toggle button
    self:addHoverEffect(toggleButton, Color3.fromRGB(80, 80, 80))

    -- Store references
    self.gui = gui
    self.mainFrame = mainFrame
    self.toggleButton = toggleButton
    
    -- Connect button events
    closeButton.MouseButton1Click:Connect(function()
        self:toggleInterface()
    end)
    
    purchaseButton.MouseButton1Click:Connect(function()
        print("Purchase button clicked!")
        print("PurchaseInterface exists:", PurchaseInterface ~= nil)
        print("showInterface method exists:", PurchaseInterface and PurchaseInterface.showInterface ~= nil)
        print("PurchaseInterface.gui:", PurchaseInterface and PurchaseInterface.gui)
        print("PurchaseInterface.purchaseFrame:", PurchaseInterface and PurchaseInterface.purchaseFrame)
        print("PurchaseInterface.overlay:", PurchaseInterface and PurchaseInterface.overlay)

        if PurchaseInterface and PurchaseInterface.showInterface then
            PurchaseInterface:showInterface()
            print("Called PurchaseInterface:showInterface()")
        else
            warn("PurchaseInterface not available or showInterface method missing")
        end
    end)
    
    -- Add hover effects
    self:addHoverEffect(closeButton, Color3.fromRGB(220, 70, 70))
    self:addHoverEffect(purchaseButton, Color3.fromRGB(70, 170, 70))
end

function MainInterface:addHoverEffect(button, hoverColor)
    local originalColor = button.BackgroundColor3
    
    button.MouseEnter:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {BackgroundColor3 = hoverColor})
        tween:Play()
    end)
    
    button.MouseLeave:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {BackgroundColor3 = originalColor})
        tween:Play()
    end)
end

function MainInterface:connectEvents()
    -- Listen for NPC status updates
    NPCStatusUpdate.OnClientEvent:Connect(function(status, data)
        if status == "purchased" then
            -- Add a small delay to ensure server has processed everything
            wait(0.5)
            self:loadNPCList() -- Refresh the list
        end
    end)

    -- Add keyboard shortcut to toggle main interface
    local UserInputService = game:GetService("UserInputService")
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        if input.KeyCode == Enum.KeyCode.N then -- N key to toggle main interface
            self:toggleInterface()
        end
    end)
end

function MainInterface:loadNPCList()
    local success, npcList = pcall(function()
        return GetNPCList:InvokeServer()
    end)

    if success and npcList then
        self:updateNPCList(npcList)
    else
        warn("Failed to load NPC list:", npcList)
        self:updateNPCList({})
    end
end

function MainInterface:updateNPCList(npcList)
    self.npcList = npcList or {}
    
    local npcListFrame = self.mainFrame:FindFirstChild("NPCListFrame")
    if not npcListFrame then return end
    
    -- Clear existing items
    for _, child in ipairs(npcListFrame:GetChildren()) do
        if child:IsA("Frame") then
            child:Destroy()
        end
    end
    
    -- Create NPC items
    for i, npc in ipairs(self.npcList) do
        self:createNPCItem(npc, i, npcListFrame)
    end
    
    -- Update canvas size
    npcListFrame.CanvasSize = UDim2.new(0, 0, 0, #self.npcList * 60 + 10)
end

function MainInterface:createNPCItem(npc, index, parent)
    local item = Instance.new("Frame")
    item.Name = "NPCItem_" .. npc.id
    item.Size = UDim2.new(1, -10, 0, 50)
    item.Position = UDim2.new(0, 5, 0, (index - 1) * 60 + 5)
    item.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    item.BorderSizePixel = 0
    item.Parent = parent
    
    local itemCorner = Instance.new("UICorner")
    itemCorner.CornerRadius = UDim.new(0, 4)
    itemCorner.Parent = item
    
    -- NPC name
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Name = "NameLabel"
    nameLabel.Size = UDim2.new(1, -100, 0.6, 0)
    nameLabel.Position = UDim2.new(0, 10, 0, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = npc.name
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.Parent = item
    
    -- NPC type
    local typeLabel = Instance.new("TextLabel")
    typeLabel.Name = "TypeLabel"
    typeLabel.Size = UDim2.new(1, -100, 0.4, 0)
    typeLabel.Position = UDim2.new(0, 10, 0.6, 0)
    typeLabel.BackgroundTransparency = 1
    typeLabel.Text = "Type: " .. (npc.type or "Unknown")
    typeLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    typeLabel.TextScaled = true
    typeLabel.Font = Enum.Font.SourceSans
    typeLabel.TextXAlignment = Enum.TextXAlignment.Left
    typeLabel.Parent = item
    
    -- Select button
    local selectButton = Instance.new("TextButton")
    selectButton.Name = "SelectButton"
    selectButton.Size = UDim2.new(0, 80, 0, 30)
    selectButton.Position = UDim2.new(1, -85, 0, 10)
    selectButton.BackgroundColor3 = Color3.fromRGB(50, 100, 200)
    selectButton.BorderSizePixel = 0
    selectButton.Text = "Select"
    selectButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    selectButton.TextScaled = true
    selectButton.Font = Enum.Font.SourceSans
    selectButton.Parent = item
    
    local selectCorner = Instance.new("UICorner")
    selectCorner.CornerRadius = UDim.new(0, 4)
    selectCorner.Parent = selectButton
    
    -- Button events
    selectButton.MouseButton1Click:Connect(function()
        self:selectNPC(npc)
    end)
    
    -- Hover effects
    self:addHoverEffect(selectButton, Color3.fromRGB(70, 120, 220))
    
    -- Item click to select
    local clickDetector = Instance.new("TextButton")
    clickDetector.Size = UDim2.new(1, 0, 1, 0)
    clickDetector.Position = UDim2.new(0, 0, 0, 0)
    clickDetector.BackgroundTransparency = 1
    clickDetector.Text = ""
    clickDetector.Parent = item
    
    clickDetector.MouseButton1Click:Connect(function()
        self:selectNPC(npc)
    end)
end

function MainInterface:selectNPC(npc)
    self.selectedNPC = npc
    
    -- Update visual selection
    self:updateSelection()
    
    -- Notify command interface
    CommandInterface:setSelectedNPC(npc)
    
    print("Selected NPC:", npc.name)
end

function MainInterface:updateSelection()
    local npcListFrame = self.mainFrame:FindFirstChild("NPCListFrame")
    if not npcListFrame then return end
    
    -- Reset all items
    for _, item in ipairs(npcListFrame:GetChildren()) do
        if item:IsA("Frame") then
            item.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
            
            local selectButton = item:FindFirstChild("SelectButton")
            if selectButton then
                selectButton.Text = "Select"
                selectButton.BackgroundColor3 = Color3.fromRGB(50, 100, 200)
            end
        end
    end
    
    -- Highlight selected item
    if self.selectedNPC then
        local selectedItem = npcListFrame:FindFirstChild("NPCItem_" .. self.selectedNPC.id)
        if selectedItem then
            selectedItem.BackgroundColor3 = Color3.fromRGB(80, 120, 80)
            
            local selectButton = selectedItem:FindFirstChild("SelectButton")
            if selectButton then
                selectButton.Text = "Selected"
                selectButton.BackgroundColor3 = Color3.fromRGB(50, 150, 50)
            end
        end
    end
end

function MainInterface:toggleInterface()
    if not self.mainFrame then return end

    local isVisible = self.mainFrame.Visible
    self.mainFrame.Visible = not isVisible

    -- Update toggle button text
    if self.toggleButton then
        if isVisible then
            self.toggleButton.Text = "NPC Menu"
        else
            self.toggleButton.Text = "Close"
        end
    end

    if not isVisible then
        -- Refresh NPC list when showing
        self:loadNPCList()
    end

    print("Main interface toggled:", not isVisible and "shown" or "hidden")
end

function MainInterface:showInterface()
    if self.mainFrame then
        self.mainFrame.Visible = true
        self:loadNPCList()
    end
end

function MainInterface:hideInterface()
    if self.mainFrame then
        self.mainFrame.Visible = false
    end
end

-- Initialize when script runs
MainInterface:init()

return MainInterface
