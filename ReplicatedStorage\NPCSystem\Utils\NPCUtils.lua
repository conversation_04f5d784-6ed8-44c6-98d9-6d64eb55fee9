-- NPCUtils.lua (ModuleScript)
-- Utility functions for the NPC system

local NPCUtils = {}

local HttpService = game:GetService("HttpService")
local RunService = game:GetService("RunService")

-- ID Generation
function NPCUtils.generateId()
    return HttpService:GenerateGUID(false)
end

function NPCUtils.generateShortId()
    local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    local id = ""
    for i = 1, 8 do
        local randomIndex = math.random(1, #chars)
        id = id .. string.sub(chars, randomIndex, randomIndex)
    end
    return id
end

-- Distance and Position Utilities
function NPCUtils.getDistance(pos1, pos2)
    if typeof(pos1) == "Vector3" and typeof(pos2) == "Vector3" then
        return (pos1 - pos2).Magnitude
    end
    return math.huge
end

function NPCUtils.getDistance2D(pos1, pos2)
    if typeof(pos1) == "Vector3" and typeof(pos2) == "Vector3" then
        local flatPos1 = Vector3.new(pos1.X, 0, pos1.Z)
        local flatPos2 = Vector3.new(pos2.X, 0, pos2.Z)
        return (flatPos1 - flatPos2).Magnitude
    end
    return math.huge
end

function NPCUtils.isWithinRange(pos1, pos2, range)
    return NPCUtils.getDistance(pos1, pos2) <= range
end

function NPCUtils.getDirectionTo(from, to)
    if typeof(from) == "Vector3" and typeof(to) == "Vector3" then
        return (to - from).Unit
    end
    return Vector3.new(0, 0, 0)
end

function NPCUtils.getRandomPositionAround(center, radius)
    if typeof(center) ~= "Vector3" then
        center = Vector3.new(0, 0, 0)
    end
    
    local angle = math.random() * math.pi * 2
    local distance = math.random() * radius
    
    local x = center.X + math.cos(angle) * distance
    local z = center.Z + math.sin(angle) * distance
    
    return Vector3.new(x, center.Y, z)
end

-- String and Parsing Utilities
function NPCUtils.parsePosition(positionString)
    if type(positionString) ~= "string" then
        return nil
    end
    
    local coords = string.split(positionString, ",")
    if #coords >= 3 then
        local x = tonumber(coords[1])
        local y = tonumber(coords[2])
        local z = tonumber(coords[3])
        
        if x and y and z then
            return Vector3.new(x, y, z)
        end
    end
    
    return nil
end

function NPCUtils.formatPosition(position)
    if typeof(position) == "Vector3" then
        return string.format("%.1f, %.1f, %.1f", position.X, position.Y, position.Z)
    end
    return "Invalid Position"
end

function NPCUtils.parseCommand(commandString)
    if type(commandString) ~= "string" then
        return nil, {}
    end
    
    local parts = string.split(commandString, " ")
    local command = parts[1]:lower()
    local args = {}
    
    for i = 2, #parts do
        table.insert(args, parts[i])
    end
    
    return command, args
end

-- Player and Object Finding
function NPCUtils.findPlayer(playerName)
    local Players = game:GetService("Players")
    
    if type(playerName) ~= "string" then
        return nil
    end
    
    -- Try exact match first
    local player = Players:FindFirstChild(playerName)
    if player then
        return player
    end
    
    -- Try partial match
    playerName = playerName:lower()
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Name:lower():find(playerName, 1, true) then
            return player
        end
    end
    
    return nil
end

function NPCUtils.findNearestPlayer(position, maxDistance)
    local Players = game:GetService("Players")
    local nearestPlayer = nil
    local nearestDistance = maxDistance or math.huge
    
    for _, player in ipairs(Players:GetPlayers()) do
        if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local distance = NPCUtils.getDistance(position, player.Character.HumanoidRootPart.Position)
            if distance < nearestDistance then
                nearestDistance = distance
                nearestPlayer = player
            end
        end
    end
    
    return nearestPlayer, nearestDistance
end

-- Time and Animation Utilities
function NPCUtils.wait(duration)
    local startTime = tick()
    while tick() - startTime < duration do
        RunService.Heartbeat:Wait()
    end
end

function NPCUtils.lerp(a, b, t)
    return a + (b - a) * t
end

function NPCUtils.smoothStep(t)
    return t * t * (3 - 2 * t)
end

-- Health and Status Utilities
function NPCUtils.getHealthPercentage(current, max)
    if max <= 0 then return 0 end
    return math.max(0, math.min(1, current / max))
end

function NPCUtils.isAlive(character)
    if not character then return false end
    
    local humanoid = character:FindFirstChild("Humanoid")
    return humanoid and humanoid.Health > 0
end

function NPCUtils.getCharacterPosition(character)
    if not character then return nil end

    local rootPart = character:FindFirstChild("HumanoidRootPart") or character:FindFirstChild("Torso") or character.PrimaryPart
    return rootPart and rootPart.Position or nil
end

-- Character Model Compatibility Utilities
function NPCUtils.getRootPart(character)
    if not character then return nil end

    -- Try HumanoidRootPart first (R15), then Torso (R6), then PrimaryPart
    return character:FindFirstChild("HumanoidRootPart") or character:FindFirstChild("Torso") or character.PrimaryPart
end

function NPCUtils.getTorso(character)
    if not character then return nil end

    -- For compatibility, return the main body part regardless of R6/R15
    return character:FindFirstChild("Torso") or character:FindFirstChild("UpperTorso") or character:FindFirstChild("HumanoidRootPart")
end

function NPCUtils.isR15Character(character)
    if not character then return false end

    -- R15 characters have UpperTorso, R6 characters have Torso
    return character:FindFirstChild("UpperTorso") ~= nil
end

function NPCUtils.isR6Character(character)
    if not character then return false end

    -- R6 characters have Torso but not UpperTorso
    return character:FindFirstChild("Torso") ~= nil and character:FindFirstChild("UpperTorso") == nil
end

-- Validation Utilities
function NPCUtils.validateNPCType(npcType)
    local NPCConfigs = require(script.Parent.Parent.Data.NPCConfigs)
    return NPCConfigs[npcType] ~= nil
end

function NPCUtils.validateCommand(command)
    local Commands = require(script.Parent.Parent.Data.Commands)
    return Commands[command:lower()] ~= nil
end

-- Color and Visual Utilities
function NPCUtils.hexToColor3(hex)
    hex = hex:gsub("#", "")
    if #hex ~= 6 then return Color3.new(1, 1, 1) end
    
    local r = tonumber(hex:sub(1, 2), 16) / 255
    local g = tonumber(hex:sub(3, 4), 16) / 255
    local b = tonumber(hex:sub(5, 6), 16) / 255
    
    return Color3.new(r, g, b)
end

function NPCUtils.color3ToHex(color)
    local r = math.floor(color.R * 255)
    local g = math.floor(color.G * 255)
    local b = math.floor(color.B * 255)
    
    return string.format("#%02X%02X%02X", r, g, b)
end

-- Table Utilities
function NPCUtils.deepCopy(original)
    local copy = {}
    for key, value in pairs(original) do
        if type(value) == "table" then
            copy[key] = NPCUtils.deepCopy(value)
        else
            copy[key] = value
        end
    end
    return copy
end

function NPCUtils.mergeTables(table1, table2)
    local result = NPCUtils.deepCopy(table1)
    for key, value in pairs(table2) do
        if type(value) == "table" and type(result[key]) == "table" then
            result[key] = NPCUtils.mergeTables(result[key], value)
        else
            result[key] = value
        end
    end
    return result
end

function NPCUtils.tableContains(table, value)
    for _, v in ipairs(table) do
        if v == value then
            return true
        end
    end
    return false
end

-- Math Utilities
function NPCUtils.clamp(value, min, max)
    return math.max(min, math.min(max, value))
end

function NPCUtils.round(number, decimals)
    local mult = 10 ^ (decimals or 0)
    return math.floor(number * mult + 0.5) / mult
end

function NPCUtils.randomFloat(min, max)
    return min + math.random() * (max - min)
end

-- Debug Utilities
function NPCUtils.debugPrint(...)
    if game:GetService("RunService"):IsStudio() then
        print("[NPCUtils Debug]", ...)
    end
end

function NPCUtils.logError(errorMessage, context)
    warn("[NPCUtils Error]", context or "Unknown", ":", errorMessage)
end

-- Performance Utilities
function NPCUtils.throttle(func, delay)
    local lastCall = 0
    return function(...)
        local now = tick()
        if now - lastCall >= delay then
            lastCall = now
            return func(...)
        end
    end
end

function NPCUtils.debounce(func, delay)
    local timer = nil
    return function(...)
        if timer then
            timer:Disconnect()
        end
        local args = {...}
        timer = task.wait(delay)
        timer = nil
        return func(unpack(args))
    end
end

return NPCUtils
