-- NPCSystemInit.lua (Script)
-- Main initialization script for the NPC System
-- This script runs automatically when placed in ServerScriptService

print("=== NPC System Initialization ===")

-- Ensure RemoteEvents are set up first
print("Ensuring RemoteEvents are set up...")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create NPCRemotes folder if it doesn't exist
local npcRemotesFolder = ReplicatedStorage:FindFirstChild("NPCRemotes")
if not npcRemotesFolder then
    print("Creating NPCRemotes folder...")
    npcRemotesFolder = Instance.new("Folder")
    npcRemotesFolder.Name = "NPCRemotes"
    npcRemotesFolder.Parent = ReplicatedStorage
end

-- Create required RemoteEvents
local remoteEvents = {"PurchaseNPC", "SendCommand", "NPCStatusUpdate"}
for _, eventName in ipairs(remoteEvents) do
    if not npcRemotesFolder:FindFirstChild(eventName) then
        print("Creating RemoteEvent:", eventName)
        local remoteEvent = Instance.new("RemoteEvent")
        remoteEvent.Name = eventName
        remoteEvent.Parent = npcRemotesFolder
    end
end

-- Create required RemoteFunction
if not npcRemotesFolder:FindFirstChild("GetNPCList") then
    print("Creating RemoteFunction: GetNPCList")
    local remoteFunction = Instance.new("RemoteFunction")
    remoteFunction.Name = "GetNPCList"
    remoteFunction.Parent = npcRemotesFolder
end

print("✓ RemoteEvents verified/created")

-- Import and initialize NPCManager
local NPCManager = require(game.ServerScriptService.NPCSystem.Core.NPCManager)
print("Initializing NPCManager...")
NPCManager:init()

print("✓ NPC System initialization complete!")
print("=== Ready to accept NPC purchases ===")
